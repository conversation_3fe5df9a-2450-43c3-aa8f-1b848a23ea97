# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved. 
#   
# Licensed under the Apache License, Version 2.0 (the "License");   
# you may not use this file except in compliance with the License.  
# You may obtain a copy of the License at   
#   
#     http://www.apache.org/licenses/LICENSE-2.0    
# 
# Unless required by applicable law or agreed to in writing, software   
# distributed under the License is distributed on an "AS IS" BASIS, 
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  
# See the License for the specific language governing permissions and   
# limitations under the License.

# from . import vgg
from . import resnet
# from . import darknet
# from . import mobilenet_v1
# from . import mobilenet_v3
# from . import hrnet
# from . import lite_hrnet
# from . import blazenet
# from . import ghostnet
# from . import senet
# from . import res2net
# from . import dla
# from . import shufflenet_v2
# from . import swin_transformer
# from . import lcnet
# from . import hardnet
# from . import esnet
# from . import cspresnet
# from . import csp_darknet
# from . import convnext
# from . import vision_transformer
# from . import mobileone
# from . import trans_encoder
# from . import focalnet
# from . import vit_mae
# from . import hgnet_v2
#
# from .vgg import *
from .resnet import *
# from .darknet import *
# from .mobilenet_v1 import *
# from .mobilenet_v3 import *
# from .hrnet import *
# from .lite_hrnet import *
# from .blazenet import *
# from .ghostnet import *
# from .senet import *
# from .res2net import *
# from .dla import *
# from .shufflenet_v2 import *
# from .swin_transformer import *
# from .lcnet import *
# from .hardnet import *
# from .esnet import *
# from .cspresnet import *
# from .csp_darknet import *
# from .convnext import *
# from .vision_transformer import *
# from .mobileone import *
# from .trans_encoder import *
# from .focalnet import *
# from .vitpose import *
# from .vit_mae import *
# from .hgnet_v2 import *
