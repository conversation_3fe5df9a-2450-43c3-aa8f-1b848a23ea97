LICENSE
README.md
setup.py
paddledet.egg-info/PKG-INFO
paddledet.egg-info/SOURCES.txt
paddledet.egg-info/dependency_links.txt
paddledet.egg-info/requires.txt
paddledet.egg-info/top_level.txt
ppdet/__init__.py
ppdet/version.py
ppdet/core/__init__.py
ppdet/core/workspace.py
ppdet/core/config/__init__.py
ppdet/core/config/schema.py
ppdet/core/config/yaml_helpers.py
ppdet/data/__init__.py
ppdet/data/reader.py
ppdet/data/shm_utils.py
ppdet/data/utils.py
ppdet/data/crop_utils/__init__.py
ppdet/data/crop_utils/annotation_cropper.py
ppdet/data/crop_utils/chip_box_utils.py
ppdet/data/source/__init__.py
ppdet/data/source/category.py
ppdet/data/source/coco.py
ppdet/data/source/dataset.py
ppdet/data/source/sniper_coco.py
ppdet/data/source/voc.py
ppdet/data/source/widerface.py
ppdet/data/transform/__init__.py
ppdet/data/transform/atss_assigner.py
ppdet/data/transform/autoaugment_utils.py
ppdet/data/transform/batch_operators.py
ppdet/data/transform/gridmask_utils.py
ppdet/data/transform/op_helper.py
ppdet/data/transform/operators.py
ppdet/engine/__init__.py
ppdet/engine/callbacks.py
ppdet/engine/env.py
ppdet/engine/export_utils.py
ppdet/engine/trainer.py
ppdet/metrics/__init__.py
ppdet/metrics/coco_utils.py
ppdet/metrics/json_results.py
ppdet/metrics/map_utils.py
ppdet/metrics/metrics.py
ppdet/metrics/munkres.py
ppdet/metrics/widerface_utils.py
ppdet/model_zoo/MODEL_ZOO
ppdet/model_zoo/__init__.py
ppdet/model_zoo/model_zoo.py
ppdet/model_zoo/tests/__init__.py
ppdet/model_zoo/tests/test_get_model.py
ppdet/model_zoo/tests/test_list_model.py
ppdet/modeling/__init__.py
ppdet/modeling/bbox_utils.py
ppdet/modeling/cls_utils.py
ppdet/modeling/initializer.py
ppdet/modeling/layers.py
ppdet/modeling/ops.py
ppdet/modeling/post_process.py
ppdet/modeling/rbox_utils.py
ppdet/modeling/shape_spec.py
ppdet/modeling/architectures/__init__.py
ppdet/modeling/architectures/damsdet.py
ppdet/modeling/architectures/detr.py
ppdet/modeling/architectures/meta_arch.py
ppdet/modeling/assigners/__init__.py
ppdet/modeling/assigners/hungarian_assigner.py
ppdet/modeling/assigners/simota_assigner.py
ppdet/modeling/assigners/utils.py
ppdet/modeling/backbones/__init__.py
ppdet/modeling/backbones/csp_darknet.py
ppdet/modeling/backbones/cspresnet.py
ppdet/modeling/backbones/name_adapter.py
ppdet/modeling/backbones/resnet.py
ppdet/modeling/heads/__init__.py
ppdet/modeling/heads/centernet_head.py
ppdet/modeling/heads/detr_head.py
ppdet/modeling/losses/__init__.py
ppdet/modeling/losses/ctfocal_loss.py
ppdet/modeling/losses/detr_loss.py
ppdet/modeling/losses/iou_aware_loss.py
ppdet/modeling/losses/iou_loss.py
ppdet/modeling/losses/varifocal_loss.py
ppdet/modeling/transformers/__init__.py
ppdet/modeling/transformers/damsdetr_transformer.py
ppdet/modeling/transformers/deformable_transformer.py
ppdet/modeling/transformers/detr_transformer.py
ppdet/modeling/transformers/hybrid_encoder.py
ppdet/modeling/transformers/matchers.py
ppdet/modeling/transformers/position_encoding.py
ppdet/modeling/transformers/utils.py
ppdet/optimizer/__init__.py
ppdet/optimizer/adamw.py
ppdet/optimizer/ema.py
ppdet/optimizer/optimizer.py
ppdet/optimizer/utils.py
ppdet/slim/__init__.py
ppdet/slim/distill_loss.py
ppdet/slim/distill_model.py
ppdet/slim/ofa.py
ppdet/slim/prune.py
ppdet/slim/quant.py
ppdet/slim/unstructured_prune.py
ppdet/utils/__init__.py
ppdet/utils/cam_utils.py
ppdet/utils/check.py
ppdet/utils/checkpoint.py
ppdet/utils/cli.py
ppdet/utils/colormap.py
ppdet/utils/download.py
ppdet/utils/fuse_utils.py
ppdet/utils/logger.py
ppdet/utils/profiler.py
ppdet/utils/stats.py
ppdet/utils/visualizer.py
ppdet/utils/voc_utils.py