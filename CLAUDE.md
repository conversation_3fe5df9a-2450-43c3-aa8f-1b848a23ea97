# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

DAMSDet 是一个基于 PaddlePaddle 的多光谱目标检测框架，实现了动态自适应多光谱检测变换器（Dynamic Adaptive Multispectral Detection Transformer）。该项目专门处理可见光（VIS）和红外（IR）双模态图像的目标检测任务。实验都需要在`damsdet`的conda虚拟环境中运行！conda activate damsdet!

## 核心命令

### 训练命令
```bash
# M3FD 数据集训练
python tools/train.py -c configs/damsdet/damsdet_r50vd_m3fd.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval

# FLIR 数据集训练  
python tools/train.py -c configs/damsdet/damsdet_r50vd_flir.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval

# LLVIP 数据集训练
python tools/train.py -c configs/damsdet/damsdet_r50vd_llvip.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval

# VEDAI 数据集训练
python tools/train.py -c configs/damsdet/damsdet_r50vd_vedai.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval
```

### 评估命令
```bash
# M3FD 数据集评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_m3fd.yml --classwise -o weights=output/M3FD/damsdet_r50vd_m3fd/best_model

# FLIR 数据集评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_flir.yml --classwise -o weights=output/FLIR/damsdet_r50vd_flir/best_model

# LLVIP 数据集评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_llvip.yml --classwise -o weights=output/LLVIP/damsdet_r50vd_llvip/best_model

# VEDAI 数据集评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_vedai.yml --classwise -o weights=output/VEDAI/damsdet_r50vd_vedai/best_model
```

### 推理命令
```bash
# M3FD 数据集推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_m3fd.yml --infer_vis_dir=dataset/coco_m3fd/val_vis_img/ --infer_ir_dir=dataset/coco_m3fd/val_ir_img --output_dir=(保存路径) -o weights=output/M3FD/damsdet_r50vd_m3fd/best_model

# FLIR 数据集推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_flir.yml --infer_vis_dir=dataset/coco_FLIR_align/val_imgs/vis_imgs --infer_ir_dir=dataset/coco_FLIR_align/val_imgs/ir_imgs --output_dir=(保存路径) -o weights=output/FLIR/damsdet_r50vd_flir/best_model

# LLVIP 数据集推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_llvip.yml --infer_vis_dir=dataset/coco_LLVIP/val_imgs/vis_imgs --infer_ir_dir=dataset/coco_LLVIP/val_imgs/ir_imgs --output_dir=(保存路径) -o weights=output/LLVIP/damsdet_r50vd_llvip/best_model

# VEDAI 数据集推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_vedai.yml --infer_vis_dir=dataset/coco_VEDAI/val_imgs/vis_imgs --infer_ir_dir=dataset/coco_VEDAI/val_imgs/ir_imgs --output_dir=(保存路径) -o weights=output/VEDAI/damsdet_r50vd_vedai/best_model
```

## 核心架构

### 主要组件
1. **DAMSDet 架构** (`ppdet/modeling/architectures/damsdet.py`)
   - 双模态输入处理（可见光 + 红外）
   - 独立的 backbone 和 neck 处理两个模态
   - 共享的 transformer 和 head 进行特征融合和检测

2. **多光谱数据集** (`ppdet/data/source/coco.py`)
   - `Mutisprctral_COCODataSet`: 多光谱 COCO 格式数据集
   - 支持双模态图像加载（vis_image_dir, ir_image_dir）

3. **DAMS-DETR Transformer** (`ppdet/modeling/transformers/damsdetr_transformer.py`)
   - 可变形多模态注意力机制
   - 自适应特征融合模块
   - 竞争性查询选择

### 配置文件结构
- `configs/damsdet/`: 模型配置文件
- `configs/datasets/`: 数据集配置文件  
- `configs/runtime_*.yml`: 运行时配置（针对不同数据集）

## 关键技术特性

1. **双模态特征提取**: 使用独立的 ResNet backbone 处理可见光和红外图像
2. **自适应特征融合**: 通过 HybridEncoder 进行跨模态特征融合
3. **可变形注意力**: MSDeformableAttention 支持多尺度特征对齐
4. **竞争性查询选择**: 动态选择最具信息量的查询进行目标检测

## 数据集支持

项目支持四个主要的多光谱数据集：
- **M3FD**: 多模态目标检测数据集
- **FLIR_align**: 对齐的 FLIR 红外数据集
- **LLVIP**: 低光可见光和红外配对数据集
- **VEDAI**: 航拍多光谱数据集

## 开发注意事项

1. **环境要求**: PaddlePaddle 2.5, CUDA 11.7, Python 3.8
2. **数据格式**: 使用 COCO 格式的标注文件，支持可见光和红外图像对
3. **预训练权重**: 需要下载 COCO 预训练权重和数据集特定的预训练权重
4. **配置继承**: 模型配置使用 YAML 继承机制，基础配置在 `_base_/` 目录下

## 文件结构关键路径

- 模型定义: `ppdet/modeling/`
- 数据处理: `ppdet/data/`
- 配置文件: `configs/`
- 工具脚本: `tools/`
- 数据集: `dataset/`