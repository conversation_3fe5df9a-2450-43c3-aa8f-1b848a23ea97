worker_num: 4
TrainReader:
  sample_transforms:
    - Multi_Decode: {}
    - Multi_RandomDistort: {prob: 0.8}
    - Multi_RandomExpand_IRVIS: {fill_value: [123.675, 116.28, 103.53], fill_value_ir: [114.495, 114.495, 114.495]}
    - Multi_RandomCrop: {prob: 0.8}
    - Multi_RandomFlip: {}
  batch_transforms:
    - Multi_BatchRandomResize: {target_size: [864, 896, 928, 960, 992, 1024, 1024, 1024, 1056, 1088, 1120, 1152, 1184], random_size: True, random_interp: True, keep_ratio: False}
    - Multi_NormalizeImage: {mean: [0., 0., 0.], std: [1., 1., 1.], norm_type: none}
    - Multi_NormalizeBox: {}
    - BboxXYXY2XYWH: {}
    - Multi_Permute: {}
  batch_size: 2
  shuffle: true
  drop_last: true
  collate_batch: false
  use_shared_memory: false


EvalReader:
  sample_transforms:
    - Multi_Decode: {}
    - Multi_Resize: {target_size: [1024, 1024], keep_ratio: False, interp: 2}
    - Multi_NormalizeImage: {mean: [0., 0., 0.], std: [1., 1., 1.], norm_type: none}
    - Multi_Permute: {}
  batch_size: 2
  shuffle: false
  drop_last: false


TestReader:
  inputs_def:
    image_shape: [3, 1024, 1024]
  sample_transforms:
    - Multi_Decode: {}
    - Multi_Resize: {target_size: [1024, 1024], keep_ratio: False, interp: 2}
    - Multi_NormalizeImage: {mean: [0., 0., 0.], std: [1., 1., 1.], norm_type: none}
    - Multi_Permute: {}
  batch_size: 1
  shuffle: false
  drop_last: false
